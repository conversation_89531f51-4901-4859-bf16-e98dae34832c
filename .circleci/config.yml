version: 2.1

orbs:
  python: circleci/python@2.1.1
  python-lib: dialogue/python-lib@0.1.55
  # coveralls: coveralls/coveralls@1.0.6

jobs:
  black:
    resource_class: small
    parameters:
      python-version:
        type: string
    docker:
      - image: cimg/python:<< parameters.python-version >>

    steps:
      - checkout

      - restore_cache:
          name: Restore cached black venv
          keys:
            - v1-pypi-py-black-<< parameters.python-version >>

      - run:
          name: Update & Activate black venv
          command: |
            python -m venv env/
            . env/bin/activate
            python -m pip install --upgrade pip
            pip install black==23.7.0

      - save_cache:
          name: Save cached black venv
          paths:
            - "env/"
          key: v1-pypi-py-black-<< parameters.python-version >>

      - run:
          name: Black format check
          command: |
            . env/bin/activate
            black --line-length 79 --exclude '(env|venv|.eggs)' --check .

  pylint:
    resource_class: small
    parameters:
      python-version:
        type: string
    docker:
      - image: cimg/python:<< parameters.python-version >>

    steps:
      - checkout

      - run:
          name: Install Pylint
          command: |
            python -m venv env/
            . env/bin/activate
            pip install pylint

      - run:
          name: Pylint check
          command: |
            . env/bin/activate
            pylint --fail-on=W,E,F --exit-zero  ./

  check_compatibility:
    parameters:
      python_version:
        type: string
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - run:
          name: Check if requirements files have changed
          command: ./scripts/check_requirements_changes.sh
      - run:
          name: Install dependencies and Check compatibility
          command: |
            if [ "$REQUIREMENTS_CHANGED" == "true" ]; then
              sudo apt-get update
              sudo apt-get install -y jq curl
              ./scripts/check_compatibility.sh << parameters.python_version >>
            else
              echo "Skipping compatibility checks..."
            fi

  build:
    resource_class: medium
    parallelism: 2
    parameters:
      python-version:
        type: string
    docker:
      - image: cimg/python:<< parameters.python-version >>

    steps:
      - checkout

      - restore_cache:
          name: Restore cached venv
          keys:
            - v1-pypi-py<< parameters.python-version >>-{{ checksum "requirements.txt" }}
            - v1-pypi-py<< parameters.python-version >>

      - run:
          name: Update & Activate venv
          command: |
            python -m venv env/
            . env/bin/activate
            python -m pip install --upgrade pip

      - save_cache:
          name: Save cached venv
          paths:
            - "env/"
          key: v1-pypi-py<< parameters.python-version >>-{{ checksum "requirements.txt" }}

      - run:
          name: Install Bittensor Subnet Template
          command: |
            . env/bin/activate
            pip install -e .

      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results

  coveralls:
    docker:
      - image: cimg/python:3.10
    steps:
      - run:
          name: Combine Coverage
          command: |
            pip3 install --upgrade coveralls
            coveralls --finish --rcfile .coveragerc || echo "Failed to upload coverage"

workflows:
  compatibility_checks:
    jobs:
      - check_compatibility:
          python_version: "3.8"
          name: check-compatibility-3.8
      - check_compatibility:
          python_version: "3.9"
          name: check-compatibility-3.9
      - check_compatibility:
          python_version: "3.10"
          name: check-compatibility-3.10
      - check_compatibility:
          python_version: "3.11"
          name: check-compatibility-3.11

  pr-requirements:
    jobs:
      - black:
          python-version: "3.8.12"
      - pylint:
          python-version: "3.8.12"
      - build:
          matrix:
            parameters:
              python-version: ["3.9.13", "3.10.6", "3.11.4"]

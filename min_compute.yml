# Use this document to specify the minimum compute requirements.
# This document will be used to generate a list of recommended hardware for your subnet.

# This is intended to give a rough estimate of the minimum requirements
# so that the user can make an informed decision about whether or not
# they want to run a miner or validator on their machine.

# NOTE: Specification for miners may be different from validators

version: '1.0' # update this version key as needed, ideally should match your release version

compute_spec:

  miner:

    cpu:
      min_cores: 4            # Minimum number of CPU cores
      min_speed: 2.5          # Minimum speed per core (GHz)
      recommended_cores: 8    # Recommended number of CPU cores
      recommended_speed: 3.5  # Recommended speed per core (GHz)
      architecture: "x86_64"  # Architecture type (e.g., x86_64, arm64)

    gpu:
      required: False                       # Does the application require a GPU?
      min_vram: 0                          # Minimum GPU VRAM (GB)
      recommended_vram: 0                  # Recommended GPU VRAM (GB)
      cuda_cores: 0                        # Minimum number of CUDA cores (if applicable)
      min_compute_capability: 0          # Minimum CUDA compute capability
      recommended_compute_capability: 0  # Recommended CUDA compute capability
      recommended_gpu: ""           # provide a recommended GPU to purchase/rent

    memory:
      min_ram: 4           # Minimum RAM (GB)
      recommended_ram: 16  # Recommended RAM (GB)
      min_swap: 4          # Minimum swap space (GB)
      recommended_swap: 8  # Recommended swap space (GB)
      ram_type: "DDR4"     # RAM type (e.g., DDR4, DDR3, etc.)

    storage:
      min_space: 5            # Minimum free storage space (GB)
      recommended_space: 20   # Recommended free storage space (GB)
      type: "SSD"             # Preferred storage type (e.g., SSD, HDD)
      min_iops: 1000          # Minimum I/O operations per second (if applicable)
      recommended_iops: 5000  # Recommended I/O operations per second

    os:
      name: "Ubuntu"  # Name of the preferred operating system(s)
      version: 22.04  # Version of the preferred operating system(s)

  validator:

    cpu:
      min_cores: 4            # Minimum number of CPU cores
      min_speed: 2.5          # Minimum speed per core (GHz)
      recommended_cores: 8    # Recommended number of CPU cores
      recommended_speed: 3.5  # Recommended speed per core (GHz)
      architecture: "x86_64"  # Architecture type (e.g., x86_64, arm64)

    gpu:
      required: False                      # Does the application require a GPU?
      min_vram: 0                          # Minimum GPU VRAM (GB)
      recommended_vram: 0                  # Recommended GPU VRAM (GB)
      cuda_cores: 0                        # Minimum number of CUDA cores (if applicable)
      min_compute_capability: 0            # Minimum CUDA compute capability
      recommended_compute_capability: 0    # Recommended CUDA compute capability
      recommended_gpu: ""                  # provide a recommended GPU to purchase/rent

    memory:
      min_ram: 16          # Minimum RAM (GB)
      min_swap: 4          # Minimum swap space (GB)
      recommended_swap: 8  # Recommended swap space (GB)
      ram_type: "DDR4"     # RAM type (e.g., DDR4, DDR3, etc.)

    storage:
      min_space: 10           # Minimum free storage space (GB)
      recommended_space: 100  # Recommended free storage space (GB)
      type: "SSD"             # Preferred storage type (e.g., SSD, HDD)
      min_iops: 1000          # Minimum I/O operations per second (if applicable)
      recommended_iops: 5000  # Recommended I/O operations per second

    os:
      name: "Ubuntu"  # Name of the preferred operating system(s)
      version: 22.04  # Version of the preferred operating system(s)

network_spec:
  bandwidth:
    download: 100  # Minimum download bandwidth (Mbps)
    upload: 20     # Minimum upload bandwidth (Mbps)

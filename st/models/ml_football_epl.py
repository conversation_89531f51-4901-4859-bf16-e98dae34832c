"""
Machine Learning Enhanced EPL Football Prediction Model
Integrates ML predictor with Sportstensor miner architecture
"""

import asyncio
import logging
import os
from typing import Optional
import bittensor as bt
from dotenv import load_dotenv

from st.sport_prediction_model import SportPredictionModel
from common.data import ProbabilityChoice, League, get_league_from_string
from common.constants import LEAGUES_ALLOWING_DRAWS

# Import our ML components
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from ml_models.football_data_fetcher import FootballDataFetcher
from ml_models.football_predictor import FootballMLPredictor

logger = logging.getLogger(__name__)

class MLFootballEPLPredictionModel(SportPredictionModel):
    """ML-enhanced EPL prediction model for Sportstensor"""
    
    def __init__(self, prediction):
        super().__init__(prediction)
        
        # Load API key from environment
        load_dotenv('neurons/miner.env')
        self.api_key = os.getenv("FOOTBALL_DATA_API_KEY", "7579c81ebeb245918c84fd0577ad2425")
        
        # Initialize ML predictor
        self.ml_predictor = FootballMLPredictor()
        
        # Competition mapping
        self.competition_code = "PL"  # Premier League code for football-data.org
        
        # Model confidence thresholds
        self.min_confidence = 0.4  # Minimum confidence to make prediction
        self.skip_threshold = 0.35  # Skip if confidence below this threshold
        
        bt.logging.info(f"Initialized ML Football EPL model for {prediction.homeTeamName} vs {prediction.awayTeamName}")
    
    async def make_prediction(self):
        """Make ML-enhanced prediction for EPL match"""
        try:
            bt.logging.info(f"Making ML prediction for {self.prediction.homeTeamName} vs {self.prediction.awayTeamName}")
            
            # Verify this is an EPL match
            if not self._is_epl_match():
                bt.logging.warning("Non-EPL match passed to EPL model, falling back to default")
                self.set_default_probability(canTie=True)
                return
            
            # Use ML prediction
            async with FootballDataFetcher(self.api_key) as fetcher:
                result = await self.ml_predictor.predict_match(
                    home_team=self.prediction.homeTeamName,
                    away_team=self.prediction.awayTeamName,
                    fetcher=fetcher,
                    competition_code=self.competition_code
                )
                
                if result:
                    # Check confidence level
                    if result.confidence < self.skip_threshold:
                        bt.logging.info(f"Low confidence ({result.confidence:.3f}), marking prediction as skip")
                        self.prediction.skip = True
                        self.set_default_probability(canTie=True)
                        return
                    
                    # Set prediction based on ML result
                    if result.predicted_outcome == "HOME":
                        self.prediction.probabilityChoice = ProbabilityChoice.HOMETEAM
                        self.prediction.probability = result.home_win_prob
                    elif result.predicted_outcome == "AWAY":
                        self.prediction.probabilityChoice = ProbabilityChoice.AWAYTEAM
                        self.prediction.probability = result.away_win_prob
                    else:  # DRAW
                        self.prediction.probabilityChoice = ProbabilityChoice.DRAW
                        self.prediction.probability = result.draw_prob
                    
                    # Apply confidence boost/penalty
                    self.prediction.probability = self._adjust_probability(
                        self.prediction.probability, 
                        result.confidence
                    )
                    
                    bt.logging.success(
                        f"ML Prediction: {self.prediction.probabilityChoice.value} "
                        f"with probability {self.prediction.probability:.3f} "
                        f"(ML confidence: {result.confidence:.3f})"
                    )
                    
                    # Log detailed prediction info
                    bt.logging.info(
                        f"Detailed probabilities - Home: {result.home_win_prob:.3f}, "
                        f"Draw: {result.draw_prob:.3f}, Away: {result.away_win_prob:.3f}"
                    )
                    
                else:
                    bt.logging.warning("ML prediction failed, falling back to default")
                    self.set_default_probability(canTie=True)
                    
        except Exception as e:
            bt.logging.error(f"Error in ML prediction: {str(e)}")
            bt.logging.warning("Falling back to default prediction")
            self.set_default_probability(canTie=True)
    
    def _is_epl_match(self) -> bool:
        """Check if this is an EPL match"""
        try:
            if isinstance(self.prediction.league, League):
                return self.prediction.league == League.EPL
            else:
                league_enum = get_league_from_string(str(self.prediction.league))
                return league_enum == League.EPL
        except:
            return False
    
    def _adjust_probability(self, base_probability: float, confidence: float) -> float:
        """Adjust probability based on model confidence"""
        # If confidence is high, we can be more aggressive with probability
        # If confidence is low, we move toward more conservative probabilities
        
        if confidence >= 0.7:
            # High confidence: boost probability slightly
            adjustment = (base_probability - 0.33) * 0.1  # Boost away from baseline
        elif confidence >= 0.5:
            # Medium confidence: slight adjustment
            adjustment = (base_probability - 0.33) * 0.05
        else:
            # Low confidence: move toward baseline
            adjustment = -(base_probability - 0.33) * 0.1
        
        adjusted_prob = base_probability + adjustment
        
        # Ensure probability stays within reasonable bounds
        return max(0.15, min(0.85, adjusted_prob))
    
    async def get_team_analysis(self, team_name: str) -> Optional[dict]:
        """Get detailed team analysis for debugging/logging"""
        try:
            async with FootballDataFetcher(self.api_key) as fetcher:
                stats = await fetcher.calculate_team_stats(team_name, self.competition_code)
                if stats:
                    return {
                        "team_name": stats.team_name,
                        "matches_played": stats.matches_played,
                        "points": stats.points,
                        "goal_difference": stats.goal_difference,
                        "form": stats.form,
                        "wins": stats.wins,
                        "draws": stats.draws,
                        "losses": stats.losses
                    }
        except Exception as e:
            bt.logging.error(f"Error getting team analysis for {team_name}: {str(e)}")
        return None

# Fallback for older Sportstensor versions that might expect different class names
EPLFootballPredictionModel = MLFootballEPLPredictionModel

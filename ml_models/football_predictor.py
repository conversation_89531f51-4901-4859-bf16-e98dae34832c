"""
Machine Learning Football Prediction Model for Sportstensor
Predicts match outcomes using historical data and team statistics
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
import pickle
import os
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

from .football_data_fetcher import FootballDataFetcher, TeamStats, MatchData

logger = logging.getLogger(__name__)

@dataclass
class MatchFeatures:
    """Features extracted for a match prediction"""
    home_team_strength: float
    away_team_strength: float
    home_form: float
    away_form: float
    home_attack_strength: float
    away_attack_strength: float
    home_defense_strength: float
    away_defense_strength: float
    home_advantage: float
    head_to_head_ratio: float
    league_avg_goals: float

@dataclass
class PredictionResult:
    """Prediction result container"""
    home_win_prob: float
    draw_prob: float
    away_win_prob: float
    predicted_outcome: str  # "HOME", "DRAW", "AWAY"
    confidence: float
    features_used: MatchFeatures

class FootballMLPredictor:
    """Machine Learning model for football match prediction"""
    
    def __init__(self, model_save_path: str = "ml_models/saved_models/"):
        self.model_save_path = model_save_path
        self.models = {}
        self.scalers = {}
        self.feature_columns = [
            'home_team_strength', 'away_team_strength', 'home_form', 'away_form',
            'home_attack_strength', 'away_attack_strength', 'home_defense_strength', 
            'away_defense_strength', 'home_advantage', 'head_to_head_ratio', 'league_avg_goals'
        ]
        
        # Create model directory if it doesn't exist
        os.makedirs(model_save_path, exist_ok=True)
        
        # Initialize models
        self._initialize_models()
        
        # Try to load pre-trained models
        self._load_models()
    
    def _initialize_models(self):
        """Initialize ML models"""
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'logistic_regression': LogisticRegression(
                random_state=42,
                class_weight='balanced',
                max_iter=1000
            )
        }
        
        # Initialize scalers for each model
        for model_name in self.models.keys():
            self.scalers[model_name] = StandardScaler()
    
    def calculate_team_strength(self, stats: TeamStats, league_avg_points: float = 45) -> float:
        """Calculate overall team strength based on statistics"""
        if stats.matches_played == 0:
            return 0.5
        
        # Points per game
        ppg = stats.points / stats.matches_played
        
        # Goal difference per game
        gd_per_game = stats.goal_difference / stats.matches_played
        
        # Form score (recent 5 matches)
        form_score = 0
        for result in stats.form:
            if result == 'W':
                form_score += 3
            elif result == 'D':
                form_score += 1
        form_score = form_score / (len(stats.form) * 3) if stats.form else 0.5
        
        # Combine metrics (normalize to 0-1)
        strength = (ppg / 3.0) * 0.5 + (gd_per_game + 3) / 6.0 * 0.3 + form_score * 0.2
        return max(0.0, min(1.0, strength))
    
    def calculate_form_score(self, form: List[str]) -> float:
        """Calculate form score from recent results"""
        if not form:
            return 0.5
        
        score = 0
        weights = [0.5, 0.7, 0.85, 0.95, 1.0]  # More recent matches have higher weight
        
        for i, result in enumerate(form[-5:]):
            weight = weights[i] if i < len(weights) else 1.0
            if result == 'W':
                score += 3 * weight
            elif result == 'D':
                score += 1 * weight
        
        max_score = sum(weights[:len(form)]) * 3
        return score / max_score if max_score > 0 else 0.5
    
    def calculate_attack_strength(self, stats: TeamStats, league_avg_goals: float = 1.5) -> float:
        """Calculate team's attacking strength"""
        if stats.matches_played == 0:
            return 0.5
        
        goals_per_game = stats.goals_for / stats.matches_played
        return min(1.0, goals_per_game / (league_avg_goals * 2))
    
    def calculate_defense_strength(self, stats: TeamStats, league_avg_goals: float = 1.5) -> float:
        """Calculate team's defensive strength (inverse of goals conceded)"""
        if stats.matches_played == 0:
            return 0.5
        
        goals_conceded_per_game = stats.goals_against / stats.matches_played
        # Invert so lower goals conceded = higher defense strength
        defense = 1.0 - min(1.0, goals_conceded_per_game / (league_avg_goals * 2))
        return max(0.0, defense)
    
    def calculate_home_advantage(self, home_stats: TeamStats, away_stats: TeamStats) -> float:
        """Calculate home advantage factor"""
        # Base home advantage
        base_advantage = 0.55
        
        # Adjust based on home team's home record
        if home_stats.home_record["wins"] + home_stats.home_record["draws"] + home_stats.home_record["losses"] > 0:
            home_games = home_stats.home_record["wins"] + home_stats.home_record["draws"] + home_stats.home_record["losses"]
            home_points = (home_stats.home_record["wins"] * 3 + home_stats.home_record["draws"]) / (home_games * 3)
            base_advantage += (home_points - 0.5) * 0.1
        
        # Adjust based on away team's away record
        if away_stats.away_record["wins"] + away_stats.away_record["draws"] + away_stats.away_record["losses"] > 0:
            away_games = away_stats.away_record["wins"] + away_stats.away_record["draws"] + away_stats.away_record["losses"]
            away_points = (away_stats.away_record["wins"] * 3 + away_stats.away_record["draws"]) / (away_games * 3)
            base_advantage -= (away_points - 0.5) * 0.05
        
        return max(0.3, min(0.7, base_advantage))
    
    async def extract_features(self, home_team: str, away_team: str, 
                             fetcher: FootballDataFetcher, competition_code: str) -> Optional[MatchFeatures]:
        """Extract features for a match"""
        try:
            # Get team statistics
            home_stats = await fetcher.calculate_team_stats(home_team, competition_code)
            away_stats = await fetcher.calculate_team_stats(away_team, competition_code)
            
            if not home_stats or not away_stats:
                logger.warning(f"Could not get stats for {home_team} vs {away_team}")
                return None
            
            # Calculate league averages (simplified)
            league_avg_goals = 1.5  # EPL average goals per team per match
            
            # Extract features
            features = MatchFeatures(
                home_team_strength=self.calculate_team_strength(home_stats),
                away_team_strength=self.calculate_team_strength(away_stats),
                home_form=self.calculate_form_score(home_stats.form),
                away_form=self.calculate_form_score(away_stats.form),
                home_attack_strength=self.calculate_attack_strength(home_stats, league_avg_goals),
                away_attack_strength=self.calculate_attack_strength(away_stats, league_avg_goals),
                home_defense_strength=self.calculate_defense_strength(home_stats, league_avg_goals),
                away_defense_strength=self.calculate_defense_strength(away_stats, league_avg_goals),
                home_advantage=self.calculate_home_advantage(home_stats, away_stats),
                head_to_head_ratio=0.5,  # Simplified - could be enhanced with H2H data
                league_avg_goals=league_avg_goals
            )
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return None
    
    def features_to_array(self, features: MatchFeatures) -> np.ndarray:
        """Convert features to numpy array for model input"""
        return np.array([
            features.home_team_strength,
            features.away_team_strength,
            features.home_form,
            features.away_form,
            features.home_attack_strength,
            features.away_attack_strength,
            features.home_defense_strength,
            features.away_defense_strength,
            features.home_advantage,
            features.head_to_head_ratio,
            features.league_avg_goals
        ]).reshape(1, -1)
    
    async def predict_match(self, home_team: str, away_team: str, 
                          fetcher: FootballDataFetcher, competition_code: str,
                          model_name: str = 'random_forest') -> Optional[PredictionResult]:
        """Predict match outcome"""
        try:
            # Extract features
            features = await self.extract_features(home_team, away_team, fetcher, competition_code)
            if not features:
                return None
            
            # Convert to array
            X = self.features_to_array(features)
            
            # Check if model is trained
            if model_name not in self.models:
                logger.error(f"Model {model_name} not found")
                return None
            
            model = self.models[model_name]
            scaler = self.scalers[model_name]
            
            # If model is not fitted, use simple heuristic prediction
            if not hasattr(model, 'classes_'):
                logger.warning(f"Model {model_name} not trained, using heuristic prediction")
                return self._heuristic_prediction(features)
            
            # Scale features
            X_scaled = scaler.transform(X)
            
            # Get probabilities
            probabilities = model.predict_proba(X_scaled)[0]
            
            # Map to outcomes (assuming classes are [0, 1, 2] for [Away Win, Draw, Home Win])
            if len(probabilities) == 3:
                away_win_prob, draw_prob, home_win_prob = probabilities
            else:
                # Binary classification fallback
                home_win_prob = probabilities[1] if len(probabilities) == 2 else features.home_team_strength
                away_win_prob = 1 - home_win_prob
                draw_prob = 0.0
            
            # Determine predicted outcome
            max_prob = max(home_win_prob, draw_prob, away_win_prob)
            if max_prob == home_win_prob:
                predicted_outcome = "HOME"
                confidence = home_win_prob
            elif max_prob == away_win_prob:
                predicted_outcome = "AWAY"
                confidence = away_win_prob
            else:
                predicted_outcome = "DRAW"
                confidence = draw_prob
            
            return PredictionResult(
                home_win_prob=home_win_prob,
                draw_prob=draw_prob,
                away_win_prob=away_win_prob,
                predicted_outcome=predicted_outcome,
                confidence=confidence,
                features_used=features
            )
            
        except Exception as e:
            logger.error(f"Error predicting match: {str(e)}")
            # Fallback to heuristic prediction
            features = await self.extract_features(home_team, away_team, fetcher, competition_code)
            if features:
                return self._heuristic_prediction(features)
            return None
    
    def _heuristic_prediction(self, features: MatchFeatures) -> PredictionResult:
        """Fallback heuristic prediction when ML model is not available"""
        # Simple heuristic based on team strengths
        strength_diff = features.home_team_strength - features.away_team_strength
        form_diff = features.home_form - features.away_form
        
        # Calculate base probabilities
        home_advantage_boost = features.home_advantage - 0.5
        total_advantage = strength_diff + form_diff * 0.3 + home_advantage_boost
        
        # Convert to probabilities (sigmoid-like function)
        home_win_prob = 0.33 + total_advantage * 0.2
        away_win_prob = 0.33 - total_advantage * 0.2
        draw_prob = 1.0 - home_win_prob - away_win_prob
        
        # Ensure probabilities are valid
        home_win_prob = max(0.1, min(0.8, home_win_prob))
        away_win_prob = max(0.1, min(0.8, away_win_prob))
        draw_prob = max(0.1, min(0.8, draw_prob))
        
        # Normalize
        total = home_win_prob + away_win_prob + draw_prob
        home_win_prob /= total
        away_win_prob /= total
        draw_prob /= total
        
        # Determine outcome
        max_prob = max(home_win_prob, draw_prob, away_win_prob)
        if max_prob == home_win_prob:
            predicted_outcome = "HOME"
            confidence = home_win_prob
        elif max_prob == away_win_prob:
            predicted_outcome = "AWAY"
            confidence = away_win_prob
        else:
            predicted_outcome = "DRAW"
            confidence = draw_prob
        
        return PredictionResult(
            home_win_prob=home_win_prob,
            draw_prob=draw_prob,
            away_win_prob=away_win_prob,
            predicted_outcome=predicted_outcome,
            confidence=confidence,
            features_used=features
        )
    
    async def prepare_training_data(self, fetcher: FootballDataFetcher, 
                                  competition_code: str, days_back: int = 365) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data from historical matches"""
        matches = await fetcher.fetch_matches(competition_code, days_back=days_back, days_forward=0)
        
        X = []
        y = []
        
        for match in matches:
            if match.status == "FINISHED" and match.home_score is not None and match.away_score is not None:
                features = await self.extract_features(match.home_team, match.away_team, fetcher, competition_code)
                if features:
                    X.append(self.features_to_array(features).flatten())
                    
                    # Determine outcome
                    if match.home_score > match.away_score:
                        outcome = 2  # Home win
                    elif match.home_score < match.away_score:
                        outcome = 0  # Away win
                    else:
                        outcome = 1  # Draw
                    
                    y.append(outcome)
        
        return np.array(X), np.array(y)
    
    async def train_models(self, fetcher: FootballDataFetcher, competition_code: str):
        """Train all ML models"""
        logger.info("Preparing training data...")
        X, y = await self.prepare_training_data(fetcher, competition_code)
        
        if len(X) == 0:
            logger.error("No training data available")
            return
        
        logger.info(f"Training with {len(X)} samples")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train each model
        for model_name, model in self.models.items():
            logger.info(f"Training {model_name}...")
            
            # Scale features
            scaler = self.scalers[model_name]
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            model.fit(X_train_scaled, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"{model_name} accuracy: {accuracy:.3f}")
        
        # Save models
        self._save_models()
    
    def _save_models(self):
        """Save trained models and scalers"""
        for model_name, model in self.models.items():
            if hasattr(model, 'classes_'):  # Check if model is trained
                model_path = os.path.join(self.model_save_path, f"{model_name}_model.pkl")
                scaler_path = os.path.join(self.model_save_path, f"{model_name}_scaler.pkl")
                
                joblib.dump(model, model_path)
                joblib.dump(self.scalers[model_name], scaler_path)
                logger.info(f"Saved {model_name} model and scaler")
    
    def _load_models(self):
        """Load pre-trained models and scalers"""
        for model_name in self.models.keys():
            model_path = os.path.join(self.model_save_path, f"{model_name}_model.pkl")
            scaler_path = os.path.join(self.model_save_path, f"{model_name}_scaler.pkl")
            
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                try:
                    self.models[model_name] = joblib.load(model_path)
                    self.scalers[model_name] = joblib.load(scaler_path)
                    logger.info(f"Loaded {model_name} model and scaler")
                except Exception as e:
                    logger.warning(f"Failed to load {model_name}: {str(e)}")

# Example usage
async def main():
    """Example usage of FootballMLPredictor"""
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    predictor = FootballMLPredictor()
    
    async with FootballDataFetcher(api_key) as fetcher:
        competition_code = "PL"
        
        # Train models (run this periodically to update models)
        # await predictor.train_models(fetcher, competition_code)
        
        # Make a prediction
        result = await predictor.predict_match("Arsenal", "Chelsea", fetcher, competition_code)
        if result:
            print(f"Prediction: {result.predicted_outcome}")
            print(f"Confidence: {result.confidence:.3f}")
            print(f"Probabilities - Home: {result.home_win_prob:.3f}, Draw: {result.draw_prob:.3f}, Away: {result.away_win_prob:.3f}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

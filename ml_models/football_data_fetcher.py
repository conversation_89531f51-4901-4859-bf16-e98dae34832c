"""
Football Data API Integration for Sportstensor
Fetches historical match data and team statistics from football-data.org
"""

import os
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import json
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TeamStats:
    """Team statistics container"""
    team_id: int
    team_name: str
    matches_played: int
    wins: int
    draws: int
    losses: int
    goals_for: int
    goals_against: int
    goal_difference: int
    points: int
    form: List[str]  # Last 5 results: W/D/L
    home_record: Dict[str, int]
    away_record: Dict[str, int]

@dataclass
class MatchData:
    """Match data container"""
    match_id: int
    home_team: str
    away_team: str
    home_score: Optional[int]
    away_score: Optional[int]
    match_date: datetime
    status: str
    competition: str

class FootballDataFetcher:
    """Fetches football data from football-data.org API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.football-data.org/v4"
        self.headers = {"X-Auth-Token": api_key}
        self.session = None
        
        # Competition mappings
        self.competitions = {
            "EPL": "PL",  # Premier League
            "English Premier League": "PL"
        }
        
    async def __aenter__(self):
        # Configure SSL context to handle certificate issues
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        self.session = aiohttp.ClientSession(headers=self.headers, connector=connector)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_competition_id(self, league: str) -> Optional[str]:
        """Get competition code from league name"""
        return self.competitions.get(league)
    
    async def fetch_team_data(self, competition_code: str, season: Optional[int] = None) -> List[Dict]:
        """Fetch teams for a competition"""
        if not season:
            season = datetime.now().year
            
        url = f"{self.base_url}/competitions/{competition_code}/teams"
        params = {"season": season}
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("teams", [])
                else:
                    logger.error(f"Error fetching teams: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Exception fetching teams: {str(e)}")
            return []
    
    async def fetch_standings(self, competition_code: str, season: Optional[int] = None) -> List[Dict]:
        """Fetch current standings for a competition"""
        if not season:
            season = datetime.now().year
            
        url = f"{self.base_url}/competitions/{competition_code}/standings"
        params = {"season": season}
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    standings = data.get("standings", [])
                    if standings:
                        return standings[0].get("table", [])
                    return []
                else:
                    logger.error(f"Error fetching standings: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Exception fetching standings: {str(e)}")
            return []
    
    async def fetch_matches(self, competition_code: str, season: Optional[int] = None, 
                          days_back: int = 30, days_forward: int = 7) -> List[MatchData]:
        """Fetch recent and upcoming matches"""
        if not season:
            season = datetime.now().year
            
        # Calculate date range
        date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        date_to = (datetime.now() + timedelta(days=days_forward)).strftime("%Y-%m-%d")
        
        url = f"{self.base_url}/competitions/{competition_code}/matches"
        params = {
            "season": season,
            "dateFrom": date_from,
            "dateTo": date_to
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    matches = []
                    
                    for match in data.get("matches", []):
                        match_data = MatchData(
                            match_id=match["id"],
                            home_team=match["homeTeam"]["name"],
                            away_team=match["awayTeam"]["name"],
                            home_score=match["score"]["fullTime"]["home"],
                            away_score=match["score"]["fullTime"]["away"],
                            match_date=datetime.fromisoformat(match["utcDate"].replace("Z", "+00:00")),
                            status=match["status"],
                            competition=competition_code
                        )
                        matches.append(match_data)
                    
                    return matches
                else:
                    logger.error(f"Error fetching matches: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Exception fetching matches: {str(e)}")
            return []
    
    async def fetch_team_matches(self, team_id: int, days_back: int = 90) -> List[MatchData]:
        """Fetch recent matches for a specific team"""
        date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        date_to = datetime.now().strftime("%Y-%m-%d")
        
        url = f"{self.base_url}/teams/{team_id}/matches"
        params = {
            "dateFrom": date_from,
            "dateTo": date_to,
            "status": "FINISHED"
        }
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    matches = []
                    
                    for match in data.get("matches", []):
                        match_data = MatchData(
                            match_id=match["id"],
                            home_team=match["homeTeam"]["name"],
                            away_team=match["awayTeam"]["name"],
                            home_score=match["score"]["fullTime"]["home"],
                            away_score=match["score"]["fullTime"]["away"],
                            match_date=datetime.fromisoformat(match["utcDate"].replace("Z", "+00:00")),
                            status=match["status"],
                            competition=match["competition"]["name"]
                        )
                        matches.append(match_data)
                    
                    return matches
                else:
                    logger.error(f"Error fetching team matches: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Exception fetching team matches: {str(e)}")
            return []
    
    def normalize_team_name(self, team_name: str) -> str:
        """Normalize team names for consistent matching"""
        # Team name mappings for consistency with Sportstensor data
        name_mappings = {
            "Arsenal FC": "Arsenal",
            "Chelsea FC": "Chelsea",
            "Liverpool FC": "Liverpool",
            "Manchester City FC": "Manchester City",
            "Manchester United FC": "Manchester United",
            "Tottenham Hotspur FC": "Tottenham",
            "West Ham United FC": "West Ham",
            "Newcastle United FC": "Newcastle",
            "Brighton & Hove Albion FC": "Brighton",
            "Wolverhampton Wanderers FC": "Wolves",
            "Leicester City FC": "Leicester",
            "Aston Villa FC": "Aston Villa",
            "Crystal Palace FC": "Crystal Palace",
            "Everton FC": "Everton",
            "Leeds United FC": "Leeds",
            "Burnley FC": "Burnley",
            "Southampton FC": "Southampton",
            "Watford FC": "Watford",
            "Norwich City FC": "Norwich",
            "Brentford FC": "Brentford",
            "Sheffield United FC": "Sheffield United",
            "Fulham FC": "Fulham",
            "Nottingham Forest FC": "Nottingham Forest",
            "Luton Town FC": "Luton",
            "AFC Bournemouth": "Bournemouth",
            "Ipswich Town FC": "Ipswich"
        }
        return name_mappings.get(team_name, team_name)
    
    async def find_team_by_name(self, team_name: str, competition_code: str) -> Optional[Dict]:
        """Find team by name in a competition"""
        teams = await self.fetch_team_data(competition_code)
        normalized_name = self.normalize_team_name(team_name)
        
        for team in teams:
            if (team["name"] == team_name or 
                team["shortName"] == team_name or 
                self.normalize_team_name(team["name"]) == normalized_name):
                return team
        return None
    
    async def calculate_team_stats(self, team_name: str, competition_code: str) -> Optional[TeamStats]:
        """Calculate comprehensive team statistics"""
        team = await self.find_team_by_name(team_name, competition_code)
        if not team:
            logger.warning(f"Team {team_name} not found in {competition_code}")
            return None
        
        # Get standings data
        standings = await self.fetch_standings(competition_code)
        team_standing = None
        for standing in standings:
            if standing["team"]["name"] == team["name"]:
                team_standing = standing
                break
        
        if not team_standing:
            logger.warning(f"Standing not found for {team_name}")
            return None
        
        # Get recent matches for form calculation
        recent_matches = await self.fetch_team_matches(team["id"], days_back=60)
        
        # Calculate form (last 5 matches)
        form = []
        home_record = {"wins": 0, "draws": 0, "losses": 0, "goals_for": 0, "goals_against": 0}
        away_record = {"wins": 0, "draws": 0, "losses": 0, "goals_for": 0, "goals_against": 0}
        
        for match in recent_matches[-5:]:  # Last 5 matches
            if match.home_team == team["name"]:
                # Home match
                if match.home_score > match.away_score:
                    form.append("W")
                    home_record["wins"] += 1
                elif match.home_score < match.away_score:
                    form.append("L")
                    home_record["losses"] += 1
                else:
                    form.append("D")
                    home_record["draws"] += 1
                home_record["goals_for"] += match.home_score or 0
                home_record["goals_against"] += match.away_score or 0
            else:
                # Away match
                if match.away_score > match.home_score:
                    form.append("W")
                    away_record["wins"] += 1
                elif match.away_score < match.home_score:
                    form.append("L")
                    away_record["losses"] += 1
                else:
                    form.append("D")
                    away_record["draws"] += 1
                away_record["goals_for"] += match.away_score or 0
                away_record["goals_against"] += match.home_score or 0
        
        return TeamStats(
            team_id=team["id"],
            team_name=team["name"],
            matches_played=team_standing["playedGames"],
            wins=team_standing["won"],
            draws=team_standing["draw"],
            losses=team_standing["lost"],
            goals_for=team_standing["goalsFor"],
            goals_against=team_standing["goalsAgainst"],
            goal_difference=team_standing["goalDifference"],
            points=team_standing["points"],
            form=form,
            home_record=home_record,
            away_record=away_record
        )

# Example usage function
async def main():
    """Example usage of FootballDataFetcher"""
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    async with FootballDataFetcher(api_key) as fetcher:
        # Test EPL data fetching
        competition_code = await fetcher.get_competition_id("EPL")
        if competition_code:
            logger.info(f"Competition code: {competition_code}")
            
            # Fetch recent matches
            matches = await fetcher.fetch_matches(competition_code, days_back=7, days_forward=7)
            logger.info(f"Found {len(matches)} matches")
            
            # Calculate team stats for Arsenal
            stats = await fetcher.calculate_team_stats("Arsenal", competition_code)
            if stats:
                logger.info(f"Arsenal stats: {stats.wins}W {stats.draws}D {stats.losses}L, Form: {stats.form}")

if __name__ == "__main__":
    asyncio.run(main())

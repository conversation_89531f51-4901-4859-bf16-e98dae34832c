#!/usr/bin/env python3
"""
Test script for ML-enhanced Sportstensor miner
Tests the integration of football-data.org API and ML prediction models
"""

import asyncio
import sys
import os
from datetime import datetime, timezone
import logging

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from common.data import MatchPrediction, League, Sport
from st.sport_prediction_model import make_match_prediction
from ml_models.football_data_fetcher import FootballDataFetcher
from ml_models.football_predictor import FootballMLPredictor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_football_data_api():
    """Test football-data.org API integration"""
    print("\n=== Testing Football Data API ===")
    
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    try:
        async with FootballDataFetcher(api_key) as fetcher:
            # Test getting competition
            competition_code = await fetcher.get_competition_id("EPL")
            print(f"✓ Competition code for EPL: {competition_code}")
            
            # Test fetching teams
            teams = await fetcher.fetch_team_data(competition_code)
            print(f"✓ Found {len(teams)} teams in EPL")
            
            # Test fetching recent matches
            matches = await fetcher.fetch_matches(competition_code, days_back=7, days_forward=7)
            print(f"✓ Found {len(matches)} recent/upcoming matches")
            
            # Test team stats calculation
            if teams:
                test_team = teams[0]["name"]
                stats = await fetcher.calculate_team_stats(test_team, competition_code)
                if stats:
                    print(f"✓ Team stats for {test_team}: {stats.wins}W {stats.draws}D {stats.losses}L")
                else:
                    print(f"✗ Failed to get stats for {test_team}")
            
            return True
            
    except Exception as e:
        print(f"✗ Football Data API test failed: {str(e)}")
        return False

async def test_ml_predictor():
    """Test ML prediction model"""
    print("\n=== Testing ML Predictor ===")
    
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    try:
        predictor = FootballMLPredictor()
        
        async with FootballDataFetcher(api_key) as fetcher:
            # Test prediction with known teams
            result = await predictor.predict_match(
                "Arsenal", "Chelsea", fetcher, "PL"
            )
            
            if result:
                print(f"✓ ML Prediction successful:")
                print(f"  Predicted outcome: {result.predicted_outcome}")
                print(f"  Confidence: {result.confidence:.3f}")
                print(f"  Probabilities - Home: {result.home_win_prob:.3f}, Draw: {result.draw_prob:.3f}, Away: {result.away_win_prob:.3f}")
                return True
            else:
                print("✗ ML prediction returned None")
                return False
                
    except Exception as e:
        print(f"✗ ML predictor test failed: {str(e)}")
        return False

async def test_sportstensor_integration():
    """Test integration with Sportstensor miner architecture"""
    print("\n=== Testing Sportstensor Integration ===")
    
    try:
        # Create a test match prediction
        test_prediction = MatchPrediction(
            matchId="test_match_123",
            matchDate=datetime.now(timezone.utc),
            sport=Sport.SOCCER,
            league=League.EPL,
            homeTeamName="Arsenal",
            awayTeamName="Chelsea"
        )
        
        print(f"Created test prediction: {test_prediction.homeTeamName} vs {test_prediction.awayTeamName}")
        
        # Run the prediction through Sportstensor's system
        result_prediction = await make_match_prediction(test_prediction)
        
        if result_prediction:
            print(f"✓ Sportstensor prediction successful:")
            print(f"  Choice: {result_prediction.probabilityChoice}")
            print(f"  Probability: {result_prediction.probability:.3f}")
            print(f"  Skip: {result_prediction.skip}")
            return True
        else:
            print("✗ Sportstensor prediction failed")
            return False
            
    except Exception as e:
        print(f"✗ Sportstensor integration test failed: {str(e)}")
        return False

async def test_error_handling():
    """Test error handling with invalid inputs"""
    print("\n=== Testing Error Handling ===")
    
    try:
        # Test with invalid team names
        test_prediction = MatchPrediction(
            matchId="test_invalid_123",
            matchDate=datetime.now(timezone.utc),
            sport=Sport.SOCCER,
            league=League.EPL,
            homeTeamName="NonExistentTeam1",
            awayTeamName="NonExistentTeam2"
        )
        
        result_prediction = await make_match_prediction(test_prediction)
        
        # Should still return a prediction (fallback)
        if result_prediction and result_prediction.probability is not None:
            print(f"✓ Error handling successful - fallback prediction made")
            print(f"  Choice: {result_prediction.probabilityChoice}")
            print(f"  Probability: {result_prediction.probability:.3f}")
            return True
        else:
            print("✗ Error handling failed - no fallback prediction")
            return False
            
    except Exception as e:
        print(f"✗ Error handling test failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🏈 Testing ML-Enhanced Sportstensor Miner")
    print("=" * 50)
    
    tests = [
        ("Football Data API", test_football_data_api),
        ("ML Predictor", test_ml_predictor),
        ("Sportstensor Integration", test_sportstensor_integration),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🏈 Test Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed! ML miner is ready.")
    else:
        print("⚠️  Some tests failed. Check the logs above.")

if __name__ == "__main__":
    asyncio.run(main())

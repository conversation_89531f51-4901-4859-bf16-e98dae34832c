#!/usr/bin/env python3
"""
Real-world test for ML-enhanced Sportstensor miner
Tests with actual live data from football-data.org API
"""

import asyncio
import sys
import os
from datetime import datetime, timezone
import logging

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from common.data import MatchPrediction, League, Sport
from st.sport_prediction_model import make_match_prediction
from ml_models.football_data_fetcher import FootballDataFetcher
from ml_models.football_predictor import FootballMLPredictor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_epl_teams():
    """Get real EPL teams and test with actual data"""
    print("\n=== Testing with Real EPL Teams ===")
    
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    try:
        async with FootballDataFetcher(api_key) as fetcher:
            # Get real EPL teams
            teams = await fetcher.fetch_team_data("PL")
            
            if not teams:
                print("✗ Could not fetch EPL teams")
                return False
            
            print(f"✓ Found {len(teams)} EPL teams")
            
            # Get first two teams for testing
            if len(teams) >= 2:
                home_team = teams[0]["name"]
                away_team = teams[1]["name"]
                
                print(f"Testing with real teams: {home_team} vs {away_team}")
                
                # Test team stats
                home_stats = await fetcher.calculate_team_stats(home_team, "PL")
                away_stats = await fetcher.calculate_team_stats(away_team, "PL")
                
                if home_stats and away_stats:
                    print(f"✓ {home_team}: {home_stats.wins}W {home_stats.draws}D {home_stats.losses}L, Points: {home_stats.points}")
                    print(f"✓ {away_team}: {away_stats.wins}W {away_stats.draws}D {away_stats.losses}L, Points: {away_stats.points}")
                    return True, home_team, away_team
                else:
                    print("✗ Could not get team statistics")
                    return False, None, None
            else:
                print("✗ Not enough teams found")
                return False, None, None
                
    except Exception as e:
        print(f"✗ Real EPL teams test failed: {str(e)}")
        return False, None, None

async def test_real_ml_prediction(home_team, away_team):
    """Test ML prediction with real team data"""
    print(f"\n=== Testing Real ML Prediction: {home_team} vs {away_team} ===")
    
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    try:
        predictor = FootballMLPredictor()
        
        async with FootballDataFetcher(api_key) as fetcher:
            # Make real prediction
            result = await predictor.predict_match(home_team, away_team, fetcher, "PL")
            
            if result:
                print(f"✓ Real ML Prediction:")
                print(f"  Teams: {home_team} vs {away_team}")
                print(f"  Predicted winner: {result.predicted_outcome}")
                print(f"  Confidence: {result.confidence:.3f}")
                print(f"  Home win: {result.home_win_prob:.3f} ({result.home_win_prob*100:.1f}%)")
                print(f"  Draw: {result.draw_prob:.3f} ({result.draw_prob*100:.1f}%)")
                print(f"  Away win: {result.away_win_prob:.3f} ({result.away_win_prob*100:.1f}%)")
                
                # Show the actual features used
                features = result.features_used
                print(f"\n  Features used:")
                print(f"    {home_team} strength: {features.home_team_strength:.3f}")
                print(f"    {away_team} strength: {features.away_team_strength:.3f}")
                print(f"    {home_team} form: {features.home_form:.3f}")
                print(f"    {away_team} form: {features.away_form:.3f}")
                print(f"    Home advantage: {features.home_advantage:.3f}")
                
                return True
            else:
                print("✗ ML prediction failed")
                return False
                
    except Exception as e:
        print(f"✗ Real ML prediction test failed: {str(e)}")
        return False

async def test_real_sportstensor_integration(home_team, away_team):
    """Test full Sportstensor integration with real teams"""
    print(f"\n=== Testing Real Sportstensor Integration: {home_team} vs {away_team} ===")
    
    try:
        # Create real match prediction
        real_prediction = MatchPrediction(
            matchId=f"real_match_{home_team}_{away_team}",
            matchDate=datetime.now(timezone.utc),
            sport=Sport.SOCCER,
            league=League.EPL,
            homeTeamName=home_team,
            awayTeamName=away_team
        )
        
        print(f"Created real prediction: {real_prediction.homeTeamName} vs {real_prediction.awayTeamName}")
        
        # Run through Sportstensor's ML-enhanced system
        result_prediction = await make_match_prediction(real_prediction)
        
        if result_prediction:
            print(f"✓ Real Sportstensor prediction successful:")
            print(f"  Match: {result_prediction.homeTeamName} vs {result_prediction.awayTeamName}")
            print(f"  Predicted choice: {result_prediction.probabilityChoice}")
            print(f"  Probability: {result_prediction.probability:.3f} ({result_prediction.probability*100:.1f}%)")
            print(f"  Skip: {result_prediction.skip}")
            
            # Validate prediction quality
            if result_prediction.probability >= 0.15 and result_prediction.probability <= 0.85:
                print(f"✓ Probability within valid range")
            else:
                print(f"⚠️  Probability outside expected range")
            
            return True
        else:
            print("✗ Real Sportstensor prediction failed")
            return False
            
    except Exception as e:
        print(f"✗ Real Sportstensor integration test failed: {str(e)}")
        return False

async def test_recent_matches():
    """Show recent/upcoming EPL matches for context"""
    print("\n=== Real Recent/Upcoming EPL Matches ===")
    
    api_key = "7579c81ebeb245918c84fd0577ad2425"
    
    try:
        async with FootballDataFetcher(api_key) as fetcher:
            matches = await fetcher.fetch_matches("PL", days_back=7, days_forward=7)
            
            if matches:
                print(f"✓ Found {len(matches)} recent/upcoming matches:")
                for i, match in enumerate(matches[:5]):  # Show first 5
                    status = "✓" if match.status == "FINISHED" else "⏳"
                    date_str = match.match_date.strftime("%Y-%m-%d %H:%M")
                    if match.home_score is not None and match.away_score is not None:
                        print(f"  {status} {match.home_team} {match.home_score}-{match.away_score} {match.away_team} ({date_str})")
                    else:
                        print(f"  {status} {match.home_team} vs {match.away_team} ({date_str})")
                return True
            else:
                print("✗ No recent matches found")
                return False
                
    except Exception as e:
        print(f"✗ Recent matches test failed: {str(e)}")
        return False

async def main():
    """Run real-world tests with live data"""
    print("🏈 Real-World ML Sportstensor Miner Test")
    print("Using live data from football-data.org")
    print("=" * 50)
    
    # Test 1: Get real EPL teams
    teams_result, home_team, away_team = await test_real_epl_teams()
    if not teams_result:
        print("❌ Cannot proceed without real team data")
        return
    
    # Test 2: Show recent matches for context
    await test_recent_matches()
    
    # Test 3: Real ML prediction
    ml_result = await test_real_ml_prediction(home_team, away_team)
    
    # Test 4: Full Sportstensor integration
    integration_result = await test_real_sportstensor_integration(home_team, away_team)
    
    # Summary
    print("\n" + "=" * 50)
    print("🏈 Real-World Test Summary")
    print("=" * 50)
    
    tests = [
        ("Real EPL Teams Data", teams_result),
        ("Real ML Prediction", ml_result),
        ("Real Sportstensor Integration", integration_result),
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)} tests")
    
    if passed == len(tests):
        print("🎉 All real-world tests passed!")
        print("🚀 Your ML miner is ready to compete with real data!")
    else:
        print("⚠️  Some tests failed. Check your API connection and configuration.")

if __name__ == "__main__":
    asyncio.run(main())

# ML-Enhanced Sportstensor Miner Setup

This guide will help you set up the machine learning enhanced Sportstensor miner that uses real football data from football-data.org to make predictions.

## 🏈 What Was Implemented

### 1. **Football Data Integration** (`ml_models/football_data_fetcher.py`)
- Fetches real-time EPL team statistics, standings, and match history
- Calculates team form, attack/defense strength, and other metrics
- Normalizes team names for consistency with Sportstensor data

### 2. **ML Prediction Model** (`ml_models/football_predictor.py`)
- Multiple ML algorithms: Random Forest, Gradient Boosting, Logistic Regression
- Feature engineering based on team statistics and form
- Confidence-based prediction system with skip logic
- Fallback heuristic predictions when ML models aren't trained

### 3. **Sportstensor Integration** (`st/models/ml_football_epl.py`)
- Seamlessly integrates with existing miner architecture
- Prioritized over standard models for EPL matches
- Implements Sportstensor's prediction interface
- Handles errors gracefully with fallbacks

## 🚀 Installation

### 1. Install ML Dependencies
```bash
cd sports-pre
pip install -r ml_requirements.txt
```

### 2. Configure API Key
The Football Data API key is already configured in `neurons/miner.env`:
```bash
FOOTBALL_DATA_API_KEY=7579c81ebeb245918c84fd0577ad2425
```

### 3. Set League Commitment
Update `neurons/miner.env` to commit to EPL:
```bash
LEAGUE_COMMITMENTS=EPL
```

## 🧪 Testing

### Test the Implementation
```bash
python test_ml_miner.py
```

This will test:
- ✅ Football Data API connectivity
- ✅ ML prediction model functionality  
- ✅ Sportstensor integration
- ✅ Error handling and fallbacks

### Expected Output
```
🏈 Testing ML-Enhanced Sportstensor Miner
==================================================

=== Testing Football Data API ===
✓ Competition code for EPL: PL
✓ Found 20 teams in EPL
✓ Found X recent/upcoming matches
✓ Team stats for Arsenal: 10W 5D 3L

=== Testing ML Predictor ===
✓ ML Prediction successful:
  Predicted outcome: HOME
  Confidence: 0.723
  Probabilities - Home: 0.456, Draw: 0.267, Away: 0.277

=== Testing Sportstensor Integration ===
✓ Sportstensor prediction successful:
  Choice: ProbabilityChoice.HOMETEAM
  Probability: 0.456
  Skip: False

=== Testing Error Handling ===
✓ Error handling successful - fallback prediction made

==================================================
🏈 Test Summary
==================================================
✓ PASS Football Data API
✓ PASS ML Predictor  
✓ PASS Sportstensor Integration
✓ PASS Error Handling

Passed: 4/4 tests
🎉 All tests passed! ML miner is ready.
```

## 🏃‍♂️ Running the Miner

### For Testing (Testnet)
```bash
pm2 start neurons/miner.py --name sportstensor-ml-miner -- \
    --netuid 172 \
    --subtensor.network test \
    --wallet.name {wallet} \
    --wallet.hotkey {hotkey} \
    --axon.port {port} \
    --axon.external_ip {ip} \
    --logging.trace \
    --blacklist.validator_min_stake 0
```

### For Production (Mainnet)
```bash
pm2 start neurons/miner.py --name sportstensor-ml-miner -- \
    --netuid 41 \
    --wallet.name {wallet} \
    --wallet.hotkey {hotkey} \
    --axon.port {port} \
    --axon.external_ip {ip} \
    --logging.trace \
    --blacklist.force_validator_permit
```

## 📊 How It Works

### Validator Request → ML Response Flow

1. **Validator sends prediction request**:
   ```
   GetMatchPrediction(
     homeTeamName="Arsenal",
     awayTeamName="Chelsea", 
     league="EPL",
     matchDate="2024-01-15T15:00:00Z"
   )
   ```

2. **ML model fetches data**:
   - Team statistics from football-data.org
   - Recent form (last 5 matches)
   - Home/away records
   - League standings and context

3. **ML model predicts**:
   ```
   Features extracted:
   - Team strengths: 0.72 vs 0.68
   - Form scores: 0.8 vs 0.6  
   - Attack strength: 0.75 vs 0.71
   - Defense strength: 0.69 vs 0.73
   - Home advantage: 0.55
   
   ML Prediction:
   - Home: 0.456 (45.6%)
   - Draw: 0.267 (26.7%) 
   - Away: 0.277 (27.7%)
   ```

4. **Miner responds**:
   ```
   MatchPrediction(
     probabilityChoice=ProbabilityChoice.HOMETEAM,
     probability=0.456,
     skip=False
   )
   ```

## ⚙️ Configuration Options

### Environment Variables (`neurons/miner.env`)
```bash
# API Configuration
FOOTBALL_DATA_API_KEY=7579c81ebeb245918c84fd0577ad2425

# ML Model Settings
ML_PREDICTIONS_ENABLED=true
ML_MIN_CONFIDENCE=0.4      # Minimum confidence to make prediction
ML_SKIP_THRESHOLD=0.35     # Skip if confidence below this

# League Commitment
LEAGUE_COMMITMENTS=EPL     # Focus on EPL for ML predictions
```

## 🔧 Model Training (Optional)

To train the ML models on historical data:

```python
from ml_models.football_predictor import FootballMLPredictor
from ml_models.football_data_fetcher import FootballDataFetcher

async def train_models():
    predictor = FootballMLPredictor()
    async with FootballDataFetcher(api_key) as fetcher:
        await predictor.train_models(fetcher, "PL")

# Run training
import asyncio
asyncio.run(train_models())
```

## 🎯 Advantages of This Implementation

### 1. **Real Data Integration**
- Uses live EPL team statistics and form
- More accurate than random predictions
- Adapts to current team performance

### 2. **Multiple ML Models**
- Random Forest for robust predictions
- Gradient Boosting for pattern recognition  
- Logistic Regression for baseline comparison

### 3. **Intelligent Skip Logic**
- Skips predictions when confidence is too low
- Maximizes prediction quality within 15% skip limit
- Fallback predictions when data is unavailable

### 4. **Seamless Integration**
- Minimal changes to existing Sportstensor code
- Backwards compatible with existing models
- Easy to extend to other leagues (MLS, etc.)

## 🛠️ Extending to Other Leagues

To add ML support for other leagues:

1. **Create new model file**: `st/models/ml_football_mls.py`
2. **Add competition mapping** in `football_data_fetcher.py`
3. **Update league classes** in `sport_prediction_model.py`
4. **Configure API endpoints** for the new league

## 📈 Performance Monitoring

The miner logs detailed prediction information:
```
INFO: Using ML-enhanced league prediction model: MLFootballEPLPredictionModel
INFO: ML Prediction: HOMETEAM with probability 0.456 (ML confidence: 0.723)
INFO: Detailed probabilities - Home: 0.456, Draw: 0.267, Away: 0.277
```

Monitor these logs to track ML model performance and adjust thresholds as needed.

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**: Install ML dependencies with `pip install -r ml_requirements.txt`
2. **API Failures**: Check internet connection and API key validity
3. **Team Name Mismatches**: Check team name normalization in `football_data_fetcher.py`
4. **Low Confidence**: Adjust `ML_MIN_CONFIDENCE` in `miner.env`

### Fallback Behavior
The system gracefully falls back to:
1. Heuristic predictions if ML models fail
2. Standard Sportstensor models if ML models unavailable
3. Random predictions if all else fails

## 📞 Support

For issues or questions:
1. Check the test output: `python test_ml_miner.py`
2. Review miner logs: `pm2 logs sportstensor-ml-miner`
3. Verify API connectivity and team name mappings
